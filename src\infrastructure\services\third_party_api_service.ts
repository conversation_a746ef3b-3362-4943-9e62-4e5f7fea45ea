// src/infrastructure/services/third_party_api_service.ts

import { SERVICE_API_PATH } from "../../config.ts";

/**
 * @description 第三方API响应接口
 */
export interface ThirdPartyTokenResponse {
	errcode: number;
	errmsg: string;
	results: string;
}

/**
 * @description 第三方API请求接口
 */
export interface ThirdPartyTokenRequest {
	email: string;
	password: string;
}

/**
 * @description 第三方API服务
 */
export class ThirdPartyApiService {
	private readonly baseUrl: string;

	constructor() {
		this.baseUrl = SERVICE_API_PATH.startsWith("http") ? SERVICE_API_PATH : `http://${SERVICE_API_PATH}`;
	}

	/**
	 * @description 获取第三方IM token
	 * @param email 邮箱（账号）
	 * @param password 密码
	 * @returns Promise<string> token
	 * @throws Error 如果请求失败或响应错误
	 */
	async getImToken(email: string, password: string): Promise<string> {
		const url = `${this.baseUrl}/v1/util/get_im_token`;

		const requestData: ThirdPartyTokenRequest = {
			email,
			password,
		};

		try {
			// 创建一个带超时的 AbortController
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

			const response = await fetch(url, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestData),
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data: ThirdPartyTokenResponse = await response.json();

			// 检查业务错误码
			if (data.errcode !== 0) {
				throw new Error(`Third party API error: ${data.errmsg} (code: ${data.errcode})`);
			}

			if (!data.results) {
				throw new Error("Third party API returned empty token");
			}

			return data.results;
		} catch (error) {
			console.error("Failed to get IM token from third party API:", error);
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error("Third party API request timeout (10 seconds)");
				}
				throw new Error(`Failed to get IM token: ${error.message}`);
			}
			throw error;
		}
	}

	/**
	 * @description 测试第三方API连接
	 * @returns Promise<boolean> 是否连接成功
	 */
	async testConnection(): Promise<boolean> {
		try {
			const response = await fetch(`${this.baseUrl}/health`, {
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
			});
			return response.ok;
		} catch (error) {
			console.error("Third party API connection test failed:", error);
			return false;
		}
	}
}
