// src/interface/http/controllers/service_account_controller.ts

import { Controller, Post, Get, Delete, ControllerMethodArgs } from "@dklab/oak-routing-ctrl";
import { container } from "../../../container.ts";
import { AddServiceAccountDTO } from "../../../application/dtos/service_account_dto.ts";
import { AddServiceAccountSchema } from "../schemas/service_account_schemas.ts";
import { SERVICE_ACCOUNT_SESSION } from "../../../config.ts";

/**
 * @description 验证Authorization头
 */
function validateAuthorization(authorization: string | null): { success: boolean; response?: any } {
	if (!authorization) {
		return {
			success: false,
			response: {
				success: false,
				code: 40100,
				message: "缺少Authorization头",
				data: null,
			},
		};
	}

	if (authorization !== SERVICE_ACCOUNT_SESSION) {
		return {
			success: false,
			response: {
				success: false,
				code: 40101,
				message: "无效的Authorization",
				data: null,
			},
		};
	}

	return { success: true };
}

// 简化的 OpenAPI 规范定义
const addServiceAccountSpec = {
	summary: "添加服务账户",
	description: "添加一个新的第三方服务账户，需要Authorization验证",
	request: {
		body: {
			content: { "application/json": { schema: AddServiceAccountSchema } },
		},
	},
};

@Controller("/api/v1/service-accounts")
export class ServiceAccountController {
	private serviceAccountService = container.serviceAccountService;

	/**
	 * @description 添加服务账户
	 */
	@Post("/add", addServiceAccountSpec)
	@ControllerMethodArgs("body", "headers")
	public async addServiceAccount(dto: AddServiceAccountDTO, headers: Headers) {
		console.log(headers);

		// 验证Authorization
		const authResult = validateAuthorization(headers.get("Authorization"));

		if (!authResult.success) {
			return authResult.response;
		}

		try {
			const validatedDto = AddServiceAccountSchema.parse(dto);
			const result = await this.serviceAccountService.addServiceAccount(validatedDto);

			return {
				success: true,
				code: 20100,
				message: "服务账户添加成功",
				data: result,
			};
		} catch (error) {
			if (error instanceof Error && error.message.includes("already exists")) {
				return {
					success: false,
					code: 40900,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}

	/**
	 * @description 获取所有服务账户
	 */
	@Get("/list")
	@ControllerMethodArgs("headers")
	public async getServiceAccounts(headers: Headers) {
		// 验证Authorization
		const authResult = validateAuthorization(headers.get("Authorization"));
		if (!authResult.success) {
			return authResult.response;
		}

		try {
			const result = await this.serviceAccountService.getAllServiceAccounts();

			return {
				success: true,
				code: 20000,
				message: "获取服务账户列表成功",
				data: result,
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @description 根据ID获取服务账户
	 */
	@Get("/:id")
	@ControllerMethodArgs("param", "headers")
	public async getServiceAccount(params: { id: string }, headers: Headers) {
		// 验证Authorization
		const authResult = validateAuthorization(headers.get("Authorization"));
		if (!authResult.success) {
			return authResult.response;
		}

		try {
			const result = await this.serviceAccountService.getServiceAccountById(params.id);

			if (!result) {
				return {
					success: false,
					code: 40400,
					message: "服务账户不存在",
					data: null,
				};
			}

			return {
				success: true,
				code: 20000,
				message: "获取服务账户成功",
				data: result,
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @description 刷新服务账户token
	 */
	@Post("/:id/refresh-token")
	@ControllerMethodArgs("param", "headers")
	public async refreshToken(params: { id: string }, headers: Headers) {
		// 验证Authorization
		const authResult = validateAuthorization(headers.get("Authorization"));
		if (!authResult.success) {
			return authResult.response;
		}

		try {
			const result = await this.serviceAccountService.refreshServiceAccountToken(params.id);

			return {
				success: true,
				code: 20000,
				message: "Token刷新成功",
				data: result,
			};
		} catch (error) {
			if (error instanceof Error && error.message.includes("not found")) {
				return {
					success: false,
					code: 40400,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}

	/**
	 * @description 删除服务账户
	 */
	@Delete("/:id")
	@ControllerMethodArgs("param", "headers")
	public async deleteServiceAccount(params: { id: string }, headers: Headers) {
		// 验证Authorization
		const authResult = validateAuthorization(headers.get("Authorization"));
		if (!authResult.success) {
			return authResult.response;
		}

		try {
			const success = await this.serviceAccountService.deleteServiceAccount(params.id);

			return {
				success: true,
				code: 20000,
				message: "服务账户删除成功",
				data: { deleted: success },
			};
		} catch (error) {
			if (error instanceof Error && error.message.includes("not found")) {
				return {
					success: false,
					code: 40400,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}
}
